2025-06-05 04:35:45,627 - INFO - Configuration dosyaları oluşturuldu veya mevcut.
2025-06-05 04:35:45,670 - INFO - Program başlangıcında Chrome işlemleri kapatılıyor...
2025-06-05 04:35:45,701 - INFO - Tails<PERSON> bağlantısı kontrol ediliyor...
2025-06-05 04:35:45,732 - INFO - Tailscale zaten yüklü.
2025-06-05 04:35:45,764 - INFO - Tailscale servisi zaten çalışıyor.
2025-06-05 04:35:45,799 - INFO - Tailscale IP: **************
2025-06-05 04:35:45,800 - INFO - Tailscale sabit IP ile çıkış sağlandı: **************
2025-06-05 04:35:46,641 - INFO - Instagram session initialization (new implementation - on-demand)
2025-06-05 04:35:46,642 - INFO - Tüm Twitter session'ları (Selenium profilleri) başlangıçta kontrol ediliyor...
2025-06-05 04:35:47,272 - INFO - DPI zoom faktörü ayarlandı: 1.0
2025-06-05 04:35:47,296 - INFO - Yeniden giriş için 8 Twitter profili bulundu.
2025-06-05 04:35:47,297 - INFO - İstatistik sistemi başlatılıyor...
2025-06-05 04:35:47,299 - INFO - İstatistik sistemi başarıyla başlatıldı.
2025-06-05 04:35:47,300 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:35:47,303 - WARNING - Profil profile1.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:35:47,304 - WARNING - Profil profile2.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:35:47,304 - WARNING - Profil profile3.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:35:47,305 - WARNING - Profil profile4.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:35:47,305 - WARNING - Profil profile5.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:35:47,306 - WARNING - Profil profile6.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:35:47,306 - WARNING - Profil profile7.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:35:47,307 - WARNING - Profil profile8.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:35:47,307 - INFO - Twitter session (Selenium profilleri) kontrol işlemi tamamlandı.
2025-06-05 04:35:49,320 - INFO - Profil-JSON senkronizasyonu başlatılıyor...
2025-06-05 04:35:49,325 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:35:49,339 - INFO - Profil-JSON senkronizasyonu tamamlandı.
2025-06-05 04:35:52,304 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:35:57,291 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:36:00,974 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:36:00,996 - INFO - İstatistik çekme kuyruğuna eklendi: twitter/BuzzHaber
2025-06-05 04:36:01,002 - WARNING - Logo yüklenemedi: x.png - [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\x.png'
2025-06-05 04:36:01,030 - INFO - Scheduler started
2025-06-05 04:36:01,032 - INFO - Scheduler başlatıldı.
2025-06-05 04:36:01,307 - INFO - Twitter istatistikleri çekiliyor: BuzzHaber
2025-06-05 04:36:02,291 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:36:02,305 - WARNING - Logo yüklenemedi: x.png - [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\x.png'
2025-06-05 04:36:07,304 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:36:07,316 - WARNING - Logo yüklenemedi: x.png - [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\x.png'
2025-06-05 04:36:12,293 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:36:12,304 - WARNING - Logo yüklenemedi: x.png - [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\x.png'
2025-06-05 04:36:17,292 - INFO - Profil-JSON senkronizasyonu başlatılıyor...
2025-06-05 04:36:17,297 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:36:17,308 - WARNING - Logo yüklenemedi: x.png - [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\x.png'
2025-06-05 04:36:17,312 - INFO - Profil-JSON senkronizasyonu tamamlandı.
2025-06-05 04:36:17,312 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:36:17,322 - WARNING - Logo yüklenemedi: x.png - [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\x.png'
2025-06-05 04:36:22,293 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:36:22,304 - WARNING - Logo yüklenemedi: x.png - [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\x.png'
2025-06-05 04:36:27,293 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:36:27,303 - WARNING - Logo yüklenemedi: x.png - [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\x.png'
2025-06-05 04:44:41,483 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\twitter\profile2.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,484 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\twitter\profile3.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,485 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\twitter\profile4.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,485 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\twitter\profile5.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,486 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\twitter\profile6.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,486 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\twitter\profile7.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,487 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\twitter\profile8.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,487 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\instagram\profile1.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,488 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\instagram\profile2.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,488 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\instagram\profile3.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,489 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\instagram\profile4.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,489 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\instagram\profile5.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,490 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\instagram\profile6.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,490 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\instagram\profile7.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,490 - INFO - C:\Users\<USER>\PycharmProjects\SorcerioModules\configuration\instagram\profile8.json boş olduğu için profil yeniden oluşturulmadı.
2025-06-05 04:44:41,491 - INFO - Configuration dosyaları oluşturuldu veya mevcut.
2025-06-05 04:44:41,497 - INFO - Twitter profil klasörü 24 saatten taze, saklanıyor: C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_twitter_stats (0.1 saat)
2025-06-05 04:44:41,498 - INFO - Program başlangıcında Chrome işlemleri kapatılıyor...
2025-06-05 04:44:41,535 - INFO - Program başlangıcında 1 Chrome profil klasörü temizleniyor...
2025-06-05 04:44:41,548 - INFO - Chrome profil klasörleri temizleme işlemi tamamlandı
2025-06-05 04:44:41,548 - INFO - Tailscale bağlantısı kontrol ediliyor...
2025-06-05 04:44:41,593 - INFO - Tailscale zaten yüklü.
2025-06-05 04:44:41,630 - INFO - Tailscale servisi zaten çalışıyor.
2025-06-05 04:44:41,663 - INFO - Tailscale IP: **************
2025-06-05 04:44:41,664 - INFO - Tailscale sabit IP ile çıkış sağlandı: **************
2025-06-05 04:44:41,766 - INFO - Instagram session initialization (new implementation - on-demand)
2025-06-05 04:44:41,767 - INFO - Tüm Twitter session'ları (Selenium profilleri) başlangıçta kontrol ediliyor...
2025-06-05 04:44:42,163 - INFO - DPI zoom faktörü ayarlandı: 1.0
2025-06-05 04:44:42,188 - INFO - Yeniden giriş için 8 Twitter profili bulundu.
2025-06-05 04:44:42,190 - INFO - İstatistik sistemi başlatılıyor...
2025-06-05 04:44:42,191 - INFO - İstatistik sistemi başarıyla başlatıldı.
2025-06-05 04:44:42,192 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:44:42,201 - INFO - İstatistik çekme kuyruğuna eklendi: twitter/BuzzHaber
2025-06-05 04:44:42,201 - INFO - Twitter istatistikleri çekiliyor: BuzzHaber
2025-06-05 04:44:42,206 - INFO - Twitter session 24 saatten taze, saklanıyor: twitter_stats_session.meta.json (0.1 saat)
2025-06-05 04:44:42,206 - INFO - Twitter profil klasörü 24 saatten taze, saklanıyor: C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_twitter_stats (0.1 saat)
2025-06-05 04:44:42,208 - INFO - Twitter'a giriş yapılıyor: BuzzHaber (profile1.json)
2025-06-05 04:44:44,224 - INFO - Profil-JSON senkronizasyonu başlatılıyor...
2025-06-05 04:44:44,230 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:44:44,246 - INFO - Profil-JSON senkronizasyonu tamamlandı.
2025-06-05 04:44:47,192 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:44:48,256 - WARNING - Chrome driver oluşturma hatası (Deneme 1/3): Chrome başlatma hatası: Message: unknown error: Chrome failed to start: crashed.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	GetHandleVerifier [0x00007FF758833A42+303250]
	(No symbol) [0x00007FF7586DA182]
	(No symbol) [0x00007FF7584CC139]
	(No symbol) [0x00007FF7584EFAF6]
	(No symbol) [0x00007FF7584EC7F1]
	(No symbol) [0x00007FF758523F27]
	(No symbol) [0x00007FF75851B923]
	(No symbol) [0x00007FF7584F5B80]
	(No symbol) [0x00007FF7584F526A]
	(No symbol) [0x00007FF7584F657B]
	GetHandleVerifier [0x00007FF75885711A+448362]
	sqlite3_dbdata_init [0x00007FF7589F852D+613741]
	sqlite3_dbdata_init [0x00007FF7589F7D92+611794]
	sqlite3_dbdata_init [0x00007FF7589F5005+600133]
	sqlite3_dbdata_init [0x00007FF7589F90DE+616734]
	(No symbol) [0x00007FF7586F3580]
	(No symbol) [0x00007FF7586E5564]
	(No symbol) [0x00007FF7586E56A7]
	(No symbol) [0x00007FF7586CB95D]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

2025-06-05 04:44:48,256 - WARNING - DevToolsActivePort hatası tespit edildi, profil temizleniyor...
2025-06-05 04:44:48,316 - INFO - Chrome driver yeniden denenecek, bekleniyor: 2 saniye...
2025-06-05 04:44:52,177 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:44:53,658 - WARNING - Chrome driver oluşturma hatası (Deneme 1/3): Chrome başlatma hatası: Message: unknown error: Chrome failed to start: crashed.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	GetHandleVerifier [0x00007FF760703A42+303250]
	(No symbol) [0x00007FF7605AA182]
	(No symbol) [0x00007FF76039C139]
	(No symbol) [0x00007FF7603BFAF6]
	(No symbol) [0x00007FF7603BC7F1]
	(No symbol) [0x00007FF7603F3F27]
	(No symbol) [0x00007FF7603EB923]
	(No symbol) [0x00007FF7603C5B80]
	(No symbol) [0x00007FF7603C526A]
	(No symbol) [0x00007FF7603C657B]
	GetHandleVerifier [0x00007FF76072711A+448362]
	sqlite3_dbdata_init [0x00007FF7608C852D+613741]
	sqlite3_dbdata_init [0x00007FF7608C7D92+611794]
	sqlite3_dbdata_init [0x00007FF7608C5005+600133]
	sqlite3_dbdata_init [0x00007FF7608C90DE+616734]
	(No symbol) [0x00007FF7605C3580]
	(No symbol) [0x00007FF7605B5564]
	(No symbol) [0x00007FF7605B56A7]
	(No symbol) [0x00007FF76059B95D]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

2025-06-05 04:44:53,658 - WARNING - DevToolsActivePort hatası tespit edildi, profil temizleniyor...
2025-06-05 04:44:53,711 - INFO - Chrome driver yeniden denenecek, bekleniyor: 2 saniye...
2025-06-05 04:44:57,180 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:44:58,422 - WARNING - Chrome driver oluşturma hatası (Deneme 2/3): Chrome başlatma hatası: Message: unknown error: Chrome failed to start: crashed.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	GetHandleVerifier [0x00007FF619933A42+303250]
	(No symbol) [0x00007FF6197DA182]
	(No symbol) [0x00007FF6195CC139]
	(No symbol) [0x00007FF6195EFAF6]
	(No symbol) [0x00007FF6195EC7F1]
	(No symbol) [0x00007FF619623F27]
	(No symbol) [0x00007FF61961B923]
	(No symbol) [0x00007FF6195F5B80]
	(No symbol) [0x00007FF6195F526A]
	(No symbol) [0x00007FF6195F657B]
	GetHandleVerifier [0x00007FF61995711A+448362]
	sqlite3_dbdata_init [0x00007FF619AF852D+613741]
	sqlite3_dbdata_init [0x00007FF619AF7D92+611794]
	sqlite3_dbdata_init [0x00007FF619AF5005+600133]
	sqlite3_dbdata_init [0x00007FF619AF90DE+616734]
	(No symbol) [0x00007FF6197F3580]
	(No symbol) [0x00007FF6197E5564]
	(No symbol) [0x00007FF6197E56A7]
	(No symbol) [0x00007FF6197CB95D]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

2025-06-05 04:44:58,422 - WARNING - DevToolsActivePort hatası tespit edildi, profil temizleniyor...
2025-06-05 04:44:58,472 - INFO - Chrome driver yeniden denenecek, bekleniyor: 4 saniye...
2025-06-05 04:45:02,181 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:45:02,995 - WARNING - Chrome driver oluşturma hatası (Deneme 2/3): Chrome başlatma hatası: Message: unknown error: Chrome failed to start: crashed.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	GetHandleVerifier [0x00007FF76F3B3A42+303250]
	(No symbol) [0x00007FF76F25A182]
	(No symbol) [0x00007FF76F04C139]
	(No symbol) [0x00007FF76F06FAF6]
	(No symbol) [0x00007FF76F06C7F1]
	(No symbol) [0x00007FF76F0A3F27]
	(No symbol) [0x00007FF76F09B923]
	(No symbol) [0x00007FF76F075B80]
	(No symbol) [0x00007FF76F07526A]
	(No symbol) [0x00007FF76F07657B]
	GetHandleVerifier [0x00007FF76F3D711A+448362]
	sqlite3_dbdata_init [0x00007FF76F57852D+613741]
	sqlite3_dbdata_init [0x00007FF76F577D92+611794]
	sqlite3_dbdata_init [0x00007FF76F575005+600133]
	sqlite3_dbdata_init [0x00007FF76F5790DE+616734]
	(No symbol) [0x00007FF76F273580]
	(No symbol) [0x00007FF76F265564]
	(No symbol) [0x00007FF76F2656A7]
	(No symbol) [0x00007FF76F24B95D]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

2025-06-05 04:45:02,995 - WARNING - DevToolsActivePort hatası tespit edildi, profil temizleniyor...
2025-06-05 04:45:03,053 - INFO - Chrome driver yeniden denenecek, bekleniyor: 4 saniye...
2025-06-05 04:45:07,185 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:45:08,154 - WARNING - Chrome driver oluşturma hatası (Deneme 3/3): Chrome başlatma hatası: Message: unknown error: Chrome failed to start: crashed.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	GetHandleVerifier [0x00007FF7C4473A42+303250]
	(No symbol) [0x00007FF7C431A182]
	(No symbol) [0x00007FF7C410C139]
	(No symbol) [0x00007FF7C412FAF6]
	(No symbol) [0x00007FF7C412C7F1]
	(No symbol) [0x00007FF7C4163F27]
	(No symbol) [0x00007FF7C415B923]
	(No symbol) [0x00007FF7C4135B80]
	(No symbol) [0x00007FF7C413526A]
	(No symbol) [0x00007FF7C413657B]
	GetHandleVerifier [0x00007FF7C449711A+448362]
	sqlite3_dbdata_init [0x00007FF7C463852D+613741]
	sqlite3_dbdata_init [0x00007FF7C4637D92+611794]
	sqlite3_dbdata_init [0x00007FF7C4635005+600133]
	sqlite3_dbdata_init [0x00007FF7C46390DE+616734]
	(No symbol) [0x00007FF7C4333580]
	(No symbol) [0x00007FF7C4325564]
	(No symbol) [0x00007FF7C43256A7]
	(No symbol) [0x00007FF7C430B95D]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

2025-06-05 04:45:08,155 - WARNING - DevToolsActivePort hatası tespit edildi, profil temizleniyor...
2025-06-05 04:45:08,215 - ERROR - Chrome driver oluşturulamadı, tüm denemeler başarısız: BuzzHaber
2025-06-05 04:45:08,215 - ERROR - Twitter için driver oluşturma veya giriş hatası (BuzzHaber): Chrome driver oluşturulamadı (3 deneme sonrası): Chrome başlatma hatası: Message: unknown error: Chrome failed to start: crashed.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	GetHandleVerifier [0x00007FF7C4473A42+303250]
	(No symbol) [0x00007FF7C431A182]
	(No symbol) [0x00007FF7C410C139]
	(No symbol) [0x00007FF7C412FAF6]
	(No symbol) [0x00007FF7C412C7F1]
	(No symbol) [0x00007FF7C4163F27]
	(No symbol) [0x00007FF7C415B923]
	(No symbol) [0x00007FF7C4135B80]
	(No symbol) [0x00007FF7C413526A]
	(No symbol) [0x00007FF7C413657B]
	GetHandleVerifier [0x00007FF7C449711A+448362]
	sqlite3_dbdata_init [0x00007FF7C463852D+613741]
	sqlite3_dbdata_init [0x00007FF7C4637D92+611794]
	sqlite3_dbdata_init [0x00007FF7C4635005+600133]
	sqlite3_dbdata_init [0x00007FF7C46390DE+616734]
	(No symbol) [0x00007FF7C4333580]
	(No symbol) [0x00007FF7C4325564]
	(No symbol) [0x00007FF7C43256A7]
	(No symbol) [0x00007FF7C430B95D]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

2025-06-05 04:45:12,182 - INFO - Profil-JSON senkronizasyonu başlatılıyor...
2025-06-05 04:45:12,189 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:45:12,205 - INFO - Profil-JSON senkronizasyonu tamamlandı.
2025-06-05 04:45:12,206 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:45:12,929 - WARNING - Chrome driver oluşturma hatası (Deneme 3/3): Chrome başlatma hatası: Message: unknown error: Chrome failed to start: crashed.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	GetHandleVerifier [0x00007FF7F2043A42+303250]
	(No symbol) [0x00007FF7F1EEA182]
	(No symbol) [0x00007FF7F1CDC139]
	(No symbol) [0x00007FF7F1CFFAF6]
	(No symbol) [0x00007FF7F1CFC7F1]
	(No symbol) [0x00007FF7F1D33F27]
	(No symbol) [0x00007FF7F1D2B923]
	(No symbol) [0x00007FF7F1D05B80]
	(No symbol) [0x00007FF7F1D0526A]
	(No symbol) [0x00007FF7F1D0657B]
	GetHandleVerifier [0x00007FF7F206711A+448362]
	sqlite3_dbdata_init [0x00007FF7F220852D+613741]
	sqlite3_dbdata_init [0x00007FF7F2207D92+611794]
	sqlite3_dbdata_init [0x00007FF7F2205005+600133]
	sqlite3_dbdata_init [0x00007FF7F22090DE+616734]
	(No symbol) [0x00007FF7F1F03580]
	(No symbol) [0x00007FF7F1EF5564]
	(No symbol) [0x00007FF7F1EF56A7]
	(No symbol) [0x00007FF7F1EDB95D]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

2025-06-05 04:45:12,929 - WARNING - DevToolsActivePort hatası tespit edildi, profil temizleniyor...
2025-06-05 04:45:12,956 - ERROR - Chrome driver oluşturulamadı, tüm denemeler başarısız: twitter_stats
2025-06-05 04:45:12,956 - ERROR - Portable Chrome driver hatası: Chrome driver oluşturulamadı (3 deneme sonrası): Chrome başlatma hatası: Message: unknown error: Chrome failed to start: crashed.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	GetHandleVerifier [0x00007FF7F2043A42+303250]
	(No symbol) [0x00007FF7F1EEA182]
	(No symbol) [0x00007FF7F1CDC139]
	(No symbol) [0x00007FF7F1CFFAF6]
	(No symbol) [0x00007FF7F1CFC7F1]
	(No symbol) [0x00007FF7F1D33F27]
	(No symbol) [0x00007FF7F1D2B923]
	(No symbol) [0x00007FF7F1D05B80]
	(No symbol) [0x00007FF7F1D0526A]
	(No symbol) [0x00007FF7F1D0657B]
	GetHandleVerifier [0x00007FF7F206711A+448362]
	sqlite3_dbdata_init [0x00007FF7F220852D+613741]
	sqlite3_dbdata_init [0x00007FF7F2207D92+611794]
	sqlite3_dbdata_init [0x00007FF7F2205005+600133]
	sqlite3_dbdata_init [0x00007FF7F22090DE+616734]
	(No symbol) [0x00007FF7F1F03580]
	(No symbol) [0x00007FF7F1EF5564]
	(No symbol) [0x00007FF7F1EF56A7]
	(No symbol) [0x00007FF7F1EDB95D]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

2025-06-05 04:45:12,956 - ERROR - İstatistik worker'da hata: Chrome driver oluşturulamadı (3 deneme sonrası): Chrome başlatma hatası: Message: unknown error: Chrome failed to start: crashed.
  (unknown error: DevToolsActivePort file doesn't exist)
  (The process started from chrome location C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
Backtrace:
	GetHandleVerifier [0x00007FF7F2043A42+303250]
	(No symbol) [0x00007FF7F1EEA182]
	(No symbol) [0x00007FF7F1CDC139]
	(No symbol) [0x00007FF7F1CFFAF6]
	(No symbol) [0x00007FF7F1CFC7F1]
	(No symbol) [0x00007FF7F1D33F27]
	(No symbol) [0x00007FF7F1D2B923]
	(No symbol) [0x00007FF7F1D05B80]
	(No symbol) [0x00007FF7F1D0526A]
	(No symbol) [0x00007FF7F1D0657B]
	GetHandleVerifier [0x00007FF7F206711A+448362]
	sqlite3_dbdata_init [0x00007FF7F220852D+613741]
	sqlite3_dbdata_init [0x00007FF7F2207D92+611794]
	sqlite3_dbdata_init [0x00007FF7F2205005+600133]
	sqlite3_dbdata_init [0x00007FF7F22090DE+616734]
	(No symbol) [0x00007FF7F1F03580]
	(No symbol) [0x00007FF7F1EF5564]
	(No symbol) [0x00007FF7F1EF56A7]
	(No symbol) [0x00007FF7F1EDB95D]
	BaseThreadInitThunk [0x00007FFE2DB47AC4+20]
	RtlUserThreadStart [0x00007FFE308EA8C1+33]

2025-06-05 04:45:13,660 - WARNING - Profil profile2.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:45:13,661 - WARNING - Profil profile3.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:45:13,661 - WARNING - Profil profile4.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:45:13,662 - WARNING - Profil profile5.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:45:13,662 - WARNING - Profil profile6.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:45:13,662 - WARNING - Profil profile7.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:45:13,663 - WARNING - Profil profile8.json için kullanıcı adı veya şifre eksik, atlanıyor.
2025-06-05 04:45:13,663 - INFO - Twitter session (Selenium profilleri) kontrol işlemi tamamlandı.
2025-06-05 04:45:17,184 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:45:22,193 - INFO - Profiller kontrol ediliyor...
2025-06-05 04:45:27,192 - INFO - Profiller kontrol ediliyor...
