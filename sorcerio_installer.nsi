; Sorcerio - Professional Windows Installer
; Company: Ozmorph
; Product: Sorcerio - Social Media Automation Tool
; Version: 1.0

!define PRODUCT_NAME "Sorcerio"
!define PRODUCT_VERSION "1.0"
!define PRODUCT_PUBLISHER "Ozmorph"
!define PRODUCT_WEB_SITE "https://ozmorph.com"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\Sorcerio.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"
!define PRODUCT_UNINST_ROOT_KEY "HKLM"

; Modern UI
!include "MUI2.nsh"
!include "FileFunc.nsh"
!include "LogicLib.nsh"
!include "WinVer.nsh"

; Installer settings
Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "Sorcerio_Setup.exe"
InstallDir "$PROGRAMFILES64\Ozmorph\Sorcerio"
InstallDirRegKey HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show
RequestExecutionLevel admin
SetCompressor /SOLID lzma

; Modern UI Configuration
!define MUI_ABORTWARNING
!define MUI_ICON "sorcerio_icon.ico"
!define MUI_UNICON "sorcerio_icon.ico"
!define MUI_HEADERIMAGE
!define MUI_HEADERIMAGE_BITMAP "header.bmp"
!define MUI_WELCOMEFINISHPAGE_BITMAP "wizard.bmp"

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "license.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; Languages
!insertmacro MUI_LANGUAGE "Turkish"
!insertmacro MUI_LANGUAGE "English"

; Component Sections
SectionGroup "Ana Program" SecGroupMain
  Section "Sorcerio Ana Program" SecMain
    SectionIn RO
    
    ; Set output path to the installation directory
    SetOutPath "$INSTDIR"
    
    ; Display installation progress
    DetailPrint "Sorcerio dosyaları kopyalanıyor..."
    
    ; Copy main application files
    File "main.py"
    File "ui.py"
    File "utils.py"
    File "download.py"
    File "upload.py"
    File "stats.py"
    File "selenium_instagram_login.py"
    File "tailscale_manager.py"
    File "requirements.txt"
    File "x.ico"
    File "instagram.ico"
    File "live_feed_events.json"
    
    ; Copy configuration directory
    SetOutPath "$INSTDIR\configuration"
    File /r "configuration\*.*"
    
    ; Copy videos directory structure
    CreateDirectory "$INSTDIR\videos"
    CreateDirectory "$INSTDIR\videos\instagramdownloaded"
    CreateDirectory "$INSTDIR\videos\youtubedownloaded"
    CreateDirectory "$INSTDIR\videos\twitterdownloaded"
    CreateDirectory "$INSTDIR\videos\temp"
    
    ; Copy live feed thumbnails
    CreateDirectory "$INSTDIR\live_feed_thumbnails"
    
    ; Create launcher script
    SetOutPath "$INSTDIR"
    FileOpen $0 "$INSTDIR\Sorcerio.bat" w
    FileWrite $0 "@echo off$\r$\n"
    FileWrite $0 "cd /d $\"$INSTDIR$\"$\r$\n"
    FileWrite $0 "python main.py$\r$\n"
    FileClose $0
    
  SectionEnd
SectionGroupEnd

Section "Python Runtime" SecPython
  DetailPrint "Python runtime kontrol ediliyor..."
  
  ; Check if Python is installed
  ReadRegStr $0 HKLM "SOFTWARE\Python\PythonCore\3.10\InstallPath" ""
  StrCmp $0 "" 0 python_found
  ReadRegStr $0 HKLM "SOFTWARE\Python\PythonCore\3.11\InstallPath" ""
  StrCmp $0 "" 0 python_found
  ReadRegStr $0 HKLM "SOFTWARE\Python\PythonCore\3.12\InstallPath" ""
  StrCmp $0 "" python_not_found python_found
  
  python_not_found:
    MessageBox MB_OK "Python 3.10+ gerekli. Lütfen önce Python'u yükleyin."
    ExecShell "open" "https://www.python.org/downloads/"
    Abort
  
  python_found:
    DetailPrint "Python bulundu: $0"
    
SectionEnd

Section "Python Paketleri" SecPackages
  DetailPrint "Python paketleri yükleniyor..."
  
  ; Install requirements
  ExecWait '"python" -m pip install --upgrade pip'
  ExecWait '"python" -m pip install -r "$INSTDIR\requirements.txt"'
  
SectionEnd

Section "Portable Chrome" SecChrome
  DetailPrint "Portable Chrome indiriliyor..."
  
  ; Create portable chromium directory
  CreateDirectory "$INSTDIR\portable_chromium"
  CreateDirectory "$INSTDIR\portable_chromium\browser"
  CreateDirectory "$INSTDIR\portable_chromium\driver"
  
  ; This will be handled by the application on first run
  DetailPrint "Portable Chrome ilk çalıştırmada otomatik indirilecek"
  
SectionEnd

Section "Masaüstü Kısayolu" SecDesktop
  CreateShortCut "$DESKTOP\Sorcerio.lnk" "$INSTDIR\Sorcerio.bat" "" "$INSTDIR\sorcerio_icon.ico"
SectionEnd

Section "Başlat Menüsü" SecStartMenu
  CreateDirectory "$SMPROGRAMS\Ozmorph"
  CreateShortCut "$SMPROGRAMS\Ozmorph\Sorcerio.lnk" "$INSTDIR\Sorcerio.bat" "" "$INSTDIR\sorcerio_icon.ico"
  CreateShortCut "$SMPROGRAMS\Ozmorph\Sorcerio Kaldır.lnk" "$INSTDIR\uninstall.exe"
SectionEnd

; Main installer function
Function .onInit
  ; Check Windows version
  ${IfNot} ${AtLeastWin10}
    MessageBox MB_OK|MB_ICONSTOP "Sorcerio Windows 10 veya üstü gerektirir."
    Abort
  ${EndIf}
  
  ; Check if already installed
  ReadRegStr $R0 ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString"
  StrCmp $R0 "" done
  
  MessageBox MB_OKCANCEL|MB_ICONEXCLAMATION \
  "Sorcerio zaten kurulu. $\n$\nÖnceki sürümü kaldırmak için OK'a tıklayın." \
  IDOK uninst
  Abort
  
uninst:
  ClearErrors
  ExecWait '$R0 _?=$INSTDIR'
  
  IfErrors no_remove_uninstaller done
    no_remove_uninstaller:
  
done:
FunctionEnd

; Post-installation tasks
Section -AdditionalIcons
  WriteIniStr "$INSTDIR\${PRODUCT_NAME}.url" "InternetShortcut" "URL" "${PRODUCT_WEB_SITE}"
  CreateShortCut "$SMPROGRAMS\Ozmorph\Ozmorph Web Sitesi.lnk" "$INSTDIR\${PRODUCT_NAME}.url"
SectionEnd

Section -Post
  WriteUninstaller "$INSTDIR\uninstall.exe"
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\Sorcerio.bat"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninstall.exe"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\sorcerio_icon.ico"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
SectionEnd

; Component descriptions
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} "Sorcerio ana program dosyaları (Gerekli)"
  !insertmacro MUI_DESCRIPTION_TEXT ${SecPython} "Python runtime ortamı (Gerekli)"
  !insertmacro MUI_DESCRIPTION_TEXT ${SecPackages} "Gerekli Python paketleri (Gerekli)"
  !insertmacro MUI_DESCRIPTION_TEXT ${SecChrome} "Portable Chrome tarayıcısı"
  !insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} "Masaüstünde kısayol oluştur"
  !insertmacro MUI_DESCRIPTION_TEXT ${SecStartMenu} "Başlat menüsünde kısayol oluştur"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; Uninstaller
Section Uninstall
  Delete "$INSTDIR\${PRODUCT_NAME}.url"
  Delete "$INSTDIR\uninstall.exe"
  Delete "$INSTDIR\*.py"
  Delete "$INSTDIR\*.bat"
  Delete "$INSTDIR\*.exe"
  Delete "$INSTDIR\*.txt"
  Delete "$INSTDIR\*.ico"
  Delete "$INSTDIR\*.json"
  
  RMDir /r "$INSTDIR\configuration"
  RMDir /r "$INSTDIR\videos"
  RMDir /r "$INSTDIR\portable_chromium"
  RMDir /r "$INSTDIR\live_feed_thumbnails"
  RMDir /r "$INSTDIR\__pycache__"
  
  Delete "$SMPROGRAMS\Ozmorph\Sorcerio.lnk"
  Delete "$SMPROGRAMS\Ozmorph\Sorcerio Kaldır.lnk"
  Delete "$SMPROGRAMS\Ozmorph\Ozmorph Web Sitesi.lnk"
  Delete "$DESKTOP\Sorcerio.lnk"
  
  RMDir "$SMPROGRAMS\Ozmorph"
  RMDir "$INSTDIR"
  
  DeleteRegKey ${PRODUCT_UNINST_ROOT_KEY} "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
SectionEnd

Function un.onUninstSuccess
  HideWindow
  MessageBox MB_ICONINFORMATION|MB_OK "$(^Name) başarıyla kaldırıldı."
FunctionEnd

Function un.onInit
  MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 "$(^Name) programını ve tüm bileşenlerini kaldırmak istediğinizden emin misiniz?" IDYES +2
  Abort
FunctionEnd
