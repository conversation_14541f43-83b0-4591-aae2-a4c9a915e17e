@echo off
title SorcerioModules - Social Media Automation Tool
echo ================================================
echo SorcerioModules - Social Media Automation Tool
echo ================================================
echo.

REM Change to the script directory
cd /d "%~dp0"

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8 or higher from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    echo After installing Python, run install_requirements.bat
    pause
    exit /b 1
)

REM Check if main.py exists
if not exist "main.py" (
    echo ERROR: main.py not found in current directory
    echo Please make sure you are running this from the SorcerioModules folder
    pause
    exit /b 1
)

echo Starting SorcerioModules...
echo.

REM Start the application
python main.py

REM If we get here, the application has closed
echo.
echo SorcerioModules has closed.
pause
