#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SorcerioModules Setup Checker
Sistem gereksinimlerini kontrol eder ve kurulum sorunlarını teşhis eder.
"""

import sys
import os
import platform
import subprocess
import importlib
from pathlib import Path

def print_header():
    print("=" * 60)
    print("🔧 SorcerioModules Setup Checker")
    print("=" * 60)
    print()

def print_section(title):
    print(f"\n📋 {title}")
    print("-" * 40)

def check_mark(condition, message):
    if condition:
        print(f"✅ {message}")
        return True
    else:
        print(f"❌ {message}")
        return False

def warning_mark(message):
    print(f"⚠️  {message}")

def info_mark(message):
    print(f"ℹ️  {message}")

def check_python_version():
    """Python sürümünü kontrol et"""
    print_section("Python Sürüm Kontrolü")
    
    version = sys.version_info
    version_str = f"{version.major}.{version.minor}.{version.micro}"
    
    print(f"Python Sürümü: {version_str}")
    print(f"Python Yolu: {sys.executable}")
    
    if version >= (3, 8):
        check_mark(True, f"Python {version_str} destekleniyor")
        return True
    else:
        check_mark(False, f"Python {version_str} çok eski! Minimum Python 3.8 gerekli")
        return False

def check_system_info():
    """Sistem bilgilerini kontrol et"""
    print_section("Sistem Bilgileri")
    
    system = platform.system()
    architecture = platform.architecture()[0]
    machine = platform.machine()
    
    print(f"İşletim Sistemi: {system}")
    print(f"Mimari: {architecture}")
    print(f"Makine Tipi: {machine}")
    
    if system == "Windows":
        check_mark(True, "Windows işletim sistemi destekleniyor")
        if architecture == "64bit":
            check_mark(True, "64-bit mimari destekleniyor")
        else:
            warning_mark("32-bit mimari. Bazı özellikler sınırlı olabilir")
        return True
    else:
        warning_mark(f"{system} işletim sistemi. Program Windows için optimize edilmiş")
        return False

def check_required_packages():
    """Gerekli Python paketlerini kontrol et"""
    print_section("Python Paketleri Kontrolü")
    
    required_packages = [
        ("PyQt5", "PyQt5"),
        ("selenium", "selenium"),
        ("requests", "requests"),
        ("Pillow", "PIL"),
        ("numpy", "numpy"),
        ("psutil", "psutil"),
        ("beautifulsoup4", "bs4"),
        ("instagrapi", "instagrapi"),
        ("yt-dlp", "yt_dlp"),
        ("moviepy", "moviepy.editor"),
        ("opencv-python", "cv2"),
        ("APScheduler", "apscheduler"),
        ("tqdm", "tqdm")
    ]
    
    missing_packages = []
    installed_count = 0
    
    for package_name, import_name in required_packages:
        try:
            importlib.import_module(import_name)
            check_mark(True, f"{package_name} yüklü")
            installed_count += 1
        except ImportError:
            check_mark(False, f"{package_name} eksik")
            missing_packages.append(package_name)
    
    print(f"\nYüklü paketler: {installed_count}/{len(required_packages)}")
    
    if missing_packages:
        print(f"\nEksik paketler: {', '.join(missing_packages)}")
        print("Eksik paketleri yüklemek için: pip install -r requirements.txt")
        return False
    else:
        check_mark(True, "Tüm gerekli paketler yüklü")
        return True

def check_file_structure():
    """Dosya yapısını kontrol et"""
    print_section("Dosya Yapısı Kontrolü")
    
    required_files = [
        "main.py",
        "ui.py",
        "utils.py",
        "download.py",
        "upload.py",
        "stats.py",
        "requirements.txt",
        "install_requirements.bat",
        "start_sorceriomodules.bat"
    ]
    
    required_dirs = [
        "configuration",
        "videos"
    ]
    
    missing_files = []
    missing_dirs = []
    
    # Dosyaları kontrol et
    for file_name in required_files:
        if os.path.exists(file_name):
            check_mark(True, f"{file_name} mevcut")
        else:
            check_mark(False, f"{file_name} eksik")
            missing_files.append(file_name)
    
    # Klasörleri kontrol et
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            check_mark(True, f"{dir_name}/ klasörü mevcut")
        else:
            warning_mark(f"{dir_name}/ klasörü eksik (otomatik oluşturulacak)")
            missing_dirs.append(dir_name)
    
    if missing_files:
        print(f"\nEksik dosyalar: {', '.join(missing_files)}")
        return False
    else:
        check_mark(True, "Tüm gerekli dosyalar mevcut")
        return True

def check_internet_connection():
    """İnternet bağlantısını kontrol et"""
    print_section("İnternet Bağlantısı Kontrolü")
    
    try:
        import requests
        response = requests.get("https://www.google.com", timeout=5)
        if response.status_code == 200:
            check_mark(True, "İnternet bağlantısı aktif")
            return True
        else:
            check_mark(False, "İnternet bağlantısı sorunlu")
            return False
    except Exception as e:
        check_mark(False, f"İnternet bağlantısı kontrol edilemedi: {str(e)}")
        return False

def check_disk_space():
    """Disk alanını kontrol et"""
    print_section("Disk Alanı Kontrolü")
    
    try:
        import shutil
        total, used, free = shutil.disk_usage(".")
        
        free_gb = free // (1024**3)
        total_gb = total // (1024**3)
        
        print(f"Toplam Alan: {total_gb} GB")
        print(f"Boş Alan: {free_gb} GB")
        
        if free_gb >= 2:
            check_mark(True, f"Yeterli disk alanı ({free_gb} GB)")
            return True
        else:
            check_mark(False, f"Yetersiz disk alanı ({free_gb} GB). Minimum 2 GB gerekli")
            return False
    except Exception as e:
        warning_mark(f"Disk alanı kontrol edilemedi: {str(e)}")
        return True

def check_permissions():
    """Dosya izinlerini kontrol et"""
    print_section("İzin Kontrolü")
    
    # Yazma izni kontrol et
    try:
        test_file = "test_write_permission.tmp"
        with open(test_file, "w") as f:
            f.write("test")
        os.remove(test_file)
        check_mark(True, "Yazma izni mevcut")
        write_permission = True
    except Exception as e:
        check_mark(False, f"Yazma izni yok: {str(e)}")
        write_permission = False
    
    # Klasör oluşturma izni kontrol et
    try:
        test_dir = "test_dir_permission"
        os.makedirs(test_dir, exist_ok=True)
        os.rmdir(test_dir)
        check_mark(True, "Klasör oluşturma izni mevcut")
        dir_permission = True
    except Exception as e:
        check_mark(False, f"Klasör oluşturma izni yok: {str(e)}")
        dir_permission = False
    
    return write_permission and dir_permission

def test_basic_functionality():
    """Temel fonksiyonaliteyi test et"""
    print_section("Temel Fonksiyonalite Testi")
    
    try:
        # Utils modülünü test et
        from utils import setup_logging, create_directory
        check_mark(True, "utils modülü yüklendi")
        
        # Loglama sistemini test et
        logger = setup_logging()
        check_mark(True, "Loglama sistemi çalışıyor")
        
        # Klasör oluşturmayı test et
        test_dir = "test_functionality"
        create_directory(test_dir)
        if os.path.exists(test_dir):
            check_mark(True, "Klasör oluşturma fonksiyonu çalışıyor")
            os.rmdir(test_dir)
        else:
            check_mark(False, "Klasör oluşturma fonksiyonu çalışmıyor")
            return False
        
        return True
        
    except Exception as e:
        check_mark(False, f"Temel fonksiyonalite testi başarısız: {str(e)}")
        return False

def provide_recommendations(results):
    """Sonuçlara göre öneriler sun"""
    print_section("Öneriler ve Çözümler")
    
    if not results["python_version"]:
        print("🔧 Python 3.8 veya üstünü yükleyin:")
        print("   https://python.org adresinden indirin")
        print("   Kurulum sırasında 'Add Python to PATH' seçeneğini işaretleyin")
    
    if not results["packages"]:
        print("🔧 Eksik paketleri yüklemek için:")
        print("   install_requirements.bat dosyasını yönetici olarak çalıştırın")
        print("   Veya: pip install -r requirements.txt")
    
    if not results["internet"]:
        print("🔧 İnternet bağlantısını kontrol edin:")
        print("   Program ilk çalıştırmada dosyaları indirmek için internet gerektirir")
    
    if not results["disk_space"]:
        print("🔧 Disk alanını temizleyin:")
        print("   En az 2 GB boş alan gereklidir")
    
    if not results["permissions"]:
        print("🔧 İzin sorunları için:")
        print("   Programı yönetici olarak çalıştırın")
        print("   Veya klasör izinlerini kontrol edin")
    
    if not results["functionality"]:
        print("🔧 Fonksiyonalite sorunları için:")
        print("   Tüm dosyaların tam olduğundan emin olun")
        print("   requirements.txt'yi yeniden yükleyin")

def main():
    """Ana kontrol fonksiyonu"""
    print_header()
    
    results = {
        "python_version": check_python_version(),
        "system_info": check_system_info(),
        "packages": check_required_packages(),
        "file_structure": check_file_structure(),
        "internet": check_internet_connection(),
        "disk_space": check_disk_space(),
        "permissions": check_permissions(),
        "functionality": test_basic_functionality()
    }
    
    print_section("Genel Durum")
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"Başarılı testler: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("\n🎉 Tüm kontroller başarılı!")
        print("✅ SorcerioModules çalıştırılmaya hazır!")
        print("\nProgramı başlatmak için:")
        print("   python main.py")
        print("   veya start_sorceriomodules.bat")
    elif passed_tests >= total_tests * 0.75:
        print("\n⚠️  Çoğu kontrol başarılı, ancak bazı sorunlar var")
        print("🔧 Aşağıdaki önerileri uygulayın:")
        provide_recommendations(results)
    else:
        print("\n❌ Kritik sorunlar tespit edildi!")
        print("🔧 Aşağıdaki sorunları çözün:")
        provide_recommendations(results)
    
    print("\n" + "=" * 60)
    input("Devam etmek için Enter tuşuna basın...")

if __name__ == "__main__":
    main() 