# PyCharm Interpreter Düzeltme Scripti
Write-Host "PyCharm Interpreter So<PERSON>u Çözülüyor..." -ForegroundColor Green
Write-Host ""

# 1. PyCharm'ın kapalı olduğunu kontrol et
$pycharmProcess = Get-Process -Name "pycharm64" -ErrorAction SilentlyContinue
if ($pycharmProcess) {
    Write-Host "UYARI: PyCharm hala açık! Lütfen PyCharm'ı kapatın ve scripti tekrar çalıştırın." -ForegroundColor Red
    exit 1
}

# 2. Mevcut Python yolunu göster
$currentPython = ".venv\Scripts\python.exe"
$fullPath = Resolve-Path $currentPython
Write-Host "Doğru Python Yolu: $fullPath" -ForegroundColor Yellow

# 3. PyCharm cache temizle
Write-Host "PyCharm cache temizleniyor..." -ForegroundColor Cyan
if (Test-Path ".idea\workspace.xml") {
    Remove-Item ".idea\workspace.xml" -Force
    Write-Host "✓ workspace.xml silindi" -ForegroundColor Green
}

# 4. Interpreter test et
Write-Host "Python interpreter test ediliyor..." -ForegroundColor Cyan
& $currentPython -c "import sys; print(f'Python: {sys.executable}'); import PIL; print('✓ PIL/Pillow yüklü')"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Python interpreter çalışıyor ve PIL yüklü!" -ForegroundColor Green
} else {
    Write-Host "✗ Python interpreter sorunu var!" -ForegroundColor Red
}

Write-Host ""
Write-Host "PyCharm'ı şimdi açabilirsiniz. Interpreter otomatik olarak ayarlanacak." -ForegroundColor Green
Write-Host "Eğer sorun devam ederse, PYCHARM_SETUP.md dosyasındaki talimatları takip edin." -ForegroundColor Yellow 