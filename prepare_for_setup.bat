@echo off
echo ========================================
echo Sorcerio Windows Setup Hazırlığı
echo ========================================
echo.

REM Geçerli dizini al
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo Mevcut dizin: %CD%
echo.

echo 1. Python sanal ortamını temizleniyor...
if exist ".venv" (
    rmdir /s /q ".venv"
    echo    ✅ .venv klasörü silindi
) else (
    echo    ℹ️  .venv klasörü bulunamadı
)

echo.
echo 2. __pycache__ dosyaları temizleniyor...
for /d /r . %%d in (__pycache__) do (
    if exist "%%d" (
        rmdir /s /q "%%d"
        echo    ✅ %%d silindi
    )
)

echo.
echo 3. .pyc dosyaları temizleniyor...
del /s /q "*.pyc" 2>nul
echo    ✅ .pyc dosyaları silindi

echo.
echo 4. IDE dosyaları temizleniyor...
if exist ".idea" (
    rmdir /s /q ".idea"
    echo    ✅ .idea klasörü silindi
)
if exist "*.iml" (
    del /q "*.iml"
    echo    ✅ .iml dosyaları silindi
)

echo.
echo 5. Kullanıcı verilerini temizleniyor...
if exist "configuration\instagram" (
    for %%f in ("configuration\instagram\*.json") do (
        if not "%%~nf"=="default" (
            del "%%f"
            echo    ✅ %%f silindi
        )
    )
)
if exist "configuration\twitter" (
    for %%f in ("configuration\twitter\*.json") do (
        if not "%%~nf"=="default" (
            del "%%f"
            echo    ✅ %%f silindi
        )
    )
)

echo.
echo 6. İndirilen içerikleri temizleniyor...
if exist "videos\instagramdownloaded" (
    del /q "videos\instagramdownloaded\*.*" 2>nul
    echo    ✅ Instagram içerikleri silindi
)
if exist "videos\twitterdownloaded" (
    del /q "videos\twitterdownloaded\*.*" 2>nul
    echo    ✅ Twitter içerikleri silindi
)
if exist "videos\youtubedownloaded" (
    del /q "videos\youtubedownloaded\*.*" 2>nul
    echo    ✅ YouTube içerikleri silindi
)

echo.
echo 7. Log dosyaları temizleniyor...
if exist "videos\social_downloader.log" (
    del "videos\social_downloader.log"
    echo    ✅ Log dosyası silindi
)

echo.
echo 8. Live feed verilerini temizleniyor...
if exist "live_feed_thumbnails" (
    del /q "live_feed_thumbnails\*.*" 2>nul
    echo    ✅ Live feed thumbnails silindi
)
echo [] > "live_feed_events.json"
echo    ✅ Live feed events sıfırlandı

echo.
echo 9. Chrome profilleri temizleniyor...
for /d %%d in (chrome_profile_*) do (
    if exist "%%d" (
        rmdir /s /q "%%d"
        echo    ✅ %%d silindi
    )
)

echo.
echo 10. First run marker siliniyor...
if exist ".first_run_complete" (
    del ".first_run_complete"
    echo    ✅ First run marker silindi
)

echo.
echo 11. İstatistikleri sıfırlanıyor...
if exist "configuration\istatistikler.json" (
    echo {"instagram": {}, "twitter": {}, "last_updated": ""} > "configuration\istatistikler.json"
    echo    ✅ İstatistikler sıfırlandı
)

echo.
echo 12. ICO dosyaları kontrol ediliyor...
if exist "x.ico" (
    echo    ✅ x.ico mevcut
) else (
    echo    ❌ x.ico bulunamadı!
)
if exist "instagram.ico" (
    echo    ✅ instagram.ico mevcut
) else (
    echo    ❌ instagram.ico bulunamadı!
)

echo.
echo ========================================
echo ✅ Windows Setup Hazırlığı Tamamlandı!
echo ========================================
echo.
echo Artık bu klasörü Windows setup'ı için kullanabilirsiniz.
echo NSIS Compiler ile sorcerio_installer.nsi dosyasını derleyebilirsiniz.
echo.
pause
