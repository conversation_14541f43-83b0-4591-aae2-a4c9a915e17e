# 🚀 SorcerioModules - Ku<PERSON><PERSON> Kılavuzu

## 📋 İçindekiler
1. [<PERSON><PERSON><PERSON> Gereksinimleri](#sistem-gereksinimleri)
2. [<PERSON><PERSON><PERSON><PERSON> (Önerilen)](#otomatik-kurulum-önerilen)
3. [<PERSON>](#manuel-kurulum)
4. [<PERSON><PERSON>alıştırma](#ilk-çalıştırma)
5. [<PERSON><PERSON> Giderme](#sorun-giderme)
6. [<PERSON><PERSON><PERSON>lan <PERSON>](#sık-sorulan-sorular)

---

## 🖥️ Sistem Gereksinimleri

### Minimum Gereksinimler:
- **<PERSON><PERSON>let<PERSON>**: Windows 10 (64-bit) veya Windows 11
- **Python**: 3.8 veya üstü
- **RAM**: 4 GB (8 GB önerilir)
- **Disk Alanı**: 2 GB boş alan
- **İnternet**: Kararlı internet bağlantısı

### Önerilen Sistem:
- **<PERSON>ş<PERSON><PERSON>**: Windows 11 (64-bit)
- **Python**: 3.9 veya 3.10
- **RAM**: 8 GB veya üstü
- **Disk Alanı**: 5 GB boş alan
- **İnternet**: Hızlı ve kararlı bağlantı

---

## 🎯 Otomatik Kurulum (Önerilen)

### Adım 1: Python Kurulumu
1. **Python'u indirin**: [python.org](https://python.org) adresine gidin
2. **Python 3.8+** sürümünü indirin (3.9 veya 3.10 önerilir)
3. **Kurulum sırasında ÖNEMLİ**: ✅ **"Add Python to PATH"** seçeneğini işaretleyin
4. Kurulumu tamamlayın ve bilgisayarı yeniden başlatın

### Adım 2: SorcerioModules Kurulumu
1. **SorcerioModules** klasörünü istediğiniz yere çıkarın (örn: `C:\SorcerioModules`)
2. **`auto_setup.bat`** dosyasına **sağ tıklayın**
3. **"Yönetici olarak çalıştır"** seçeneğini seçin
4. Kurulum scriptinin talimatlarını takip edin
5. Kurulum tamamlandığında program otomatik başlatılabilir

### Adım 3: Kurulum Doğrulama
- Kurulum tamamlandıktan sonra `setup_checker.py` çalıştırılacak
- Tüm kontroller ✅ işareti alırsa kurulum başarılıdır
- Sorun varsa ekrandaki önerileri uygulayın

---

## 🔧 Manuel Kurulum

### Adım 1: Python Kontrolü
```cmd
python --version
```
Bu komut Python sürümünü göstermelidir. Hata alırsanız Python'u yükleyin.

### Adım 2: Pip Güncelleme
```cmd
python -m pip install --upgrade pip
```

### Adım 3: Paket Yükleme
```cmd
pip install -r requirements.txt
```

### Adım 4: Kurulum Kontrolü
```cmd
python setup_checker.py
```

---

## 🎮 İlk Çalıştırma

### Programı Başlatma
**Seçenek 1**: `start_sorceriomodules.bat` dosyasını çift tıklayın
**Seçenek 2**: Komut satırından `python main.py` yazın

### İlk Çalıştırmada Olacaklar:
1. **Portable Chrome İndirme**: Program kendi Chrome tarayıcısını indirecek
2. **FFmpeg İndirme**: Video işleme araçları indirilecek
3. **Klasör Oluşturma**: Gerekli klasörler otomatik oluşturulacak
4. **Konfigürasyon**: Varsayılan ayarlar hazırlanacak

### ⏱️ İlk Çalıştırma Süresi:
- **Hızlı internet**: 2-5 dakika
- **Yavaş internet**: 10-15 dakika

---

## 🔧 Sorun Giderme

### ❌ "Python bulunamadı" Hatası
**Çözüm**:
1. Python'u [python.org](https://python.org) adresinden indirin
2. Kurulum sırasında **"Add Python to PATH"** seçeneğini işaretleyin
3. Bilgisayarı yeniden başlatın
4. Kurulumu tekrar deneyin

### ❌ "Paket yükleme hatası"
**Çözüm**:
1. İnternet bağlantınızı kontrol edin
2. Antivirüs yazılımınızı geçici olarak kapatın
3. Windows Defender'ı geçici olarak kapatın
4. VPN kullanıyorsanız kapatın
5. Komutu yönetici olarak çalıştırın

### ❌ "Chrome indirme hatası"
**Çözüm**:
1. İnternet bağlantınızı kontrol edin
2. Güvenlik duvarı ayarlarını kontrol edin
3. Antivirüs yazılımının indirmeyi engellemediğinden emin olun
4. Program klasörüne yazma izni olduğundan emin olun

### ❌ "FFmpeg indirme hatası"
**Çözüm**:
1. İnternet bağlantınızı kontrol edin
2. Güvenlik duvarı ayarlarını kontrol edin
3. Program klasörüne yazma izni olduğundan emin olun

### ❌ "Yetersiz disk alanı"
**Çözüm**:
1. En az 2 GB boş disk alanı sağlayın
2. Geçici dosyaları temizleyin
3. Gereksiz programları kaldırın

---

## ❓ Sık Sorulan Sorular

### S: Hangi Python sürümünü kullanmalıyım?
**C**: Python 3.9 veya 3.10 önerilir. Python 3.8 minimum gereksinimdir.

### S: Program neden yavaş çalışıyor?
**C**: 
- RAM miktarınızı kontrol edin (8 GB önerilir)
- Çok fazla program aynı anda çalıştırmayın
- SSD kullanıyorsanız performans daha iyi olacaktır

### S: Antivirüs yazılımım programı engelliyor?
**C**: 
- Program klasörünü antivirüs istisnalarına ekleyin
- Portable Chrome indirme sırasında geçici olarak antivirüsü kapatın
- Windows Defender'da da istisna ekleyin

### S: Program çöküyor, ne yapmalıyım?
**C**: 
1. `setup_checker.py` çalıştırın
2. Log dosyalarını kontrol edin (`videos/social_downloader.log`)
3. Tüm gereksinimlerin yüklü olduğundan emin olun
4. Programı yönetici olarak çalıştırmayı deneyin

### S: İnternet bağlantım yavaş, ne yapabilirim?
**C**: 
- İlk kurulumu hızlı internet bağlantısı olan bir yerde yapın
- Kurulum tamamlandıktan sonra program offline çalışabilir
- Sadece içerik indirme ve yükleme için internet gerekir

### S: Program güvenli mi?
**C**: 
- Evet, program tamamen güvenlidir
- Kaynak kodu açık ve incelenebilir
- Kişisel verileriniz sadece yerel olarak saklanır
- Hiçbir veri dış sunuculara gönderilmez

---

## 📞 Destek

### Sorun yaşıyorsanız:
1. **İlk olarak**: `setup_checker.py` çalıştırın
2. **Log dosyalarını kontrol edin**: `videos/social_downloader.log`
3. **Bu kılavuzdaki çözümleri deneyin**
4. **Sistem gereksinimlerini kontrol edin**

### Dosya Yapısı Kontrolü:
```
SorcerioModules/
├── main.py                    ✅ Ana program
├── setup_checker.py           ✅ Kurulum kontrolü
├── auto_setup.bat            ✅ Otomatik kurulum
├── start_sorceriomodules.bat ✅ Program başlatıcı
├── requirements.txt          ✅ Gerekli paketler
├── README.md                 ✅ İngilizce kılavuz
├── KURULUM_KILAVUZU.md      ✅ Türkçe kılavuz
└── configuration/           ✅ Ayar dosyaları
```

---

## 🎉 Başarılı Kurulum Sonrası

Kurulum başarıyla tamamlandıktan sonra:

1. **Profil Ayarları**: Instagram ve Twitter hesaplarınızı ekleyin
2. **İçerik Linkleri**: İndirmek istediğiniz içerik linklerini ekleyin
3. **Zamanlama**: Paylaşım zamanlarınızı ayarlayın
4. **Test**: Küçük bir test ile sistemi deneyin

**🚀 Artık SorcerioModules'u kullanmaya hazırsınız!**

---

*Son güncelleme: 2025*
*Sürüm: 1.0* 