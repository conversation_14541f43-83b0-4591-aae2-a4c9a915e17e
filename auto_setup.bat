@echo off
title SorcerioModules - Otomatik Kurulum
color 0A

echo ================================================================
echo 🚀 SorcerioModules - Otomatik Kurulum ve Sistem Hazırlığı
echo ================================================================
echo.
echo Bu script aşağıdaki işlemleri gerçekleştirecek:
echo ✅ Python kurulumunu kontrol edecek
echo ✅ Gerekli Python paketlerini yükleyecek
echo ✅ Sistem gereksinimlerini kontrol edecek
echo ✅ Programı çalıştırmaya hazır hale getirecek
echo.
echo Devam etmek için herhangi bir tuşa basın...
pause >nul
echo.

REM Yönetici yetkisi kontrolü
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Yönetici yetkileri mevcut
) else (
    echo ⚠️  Bu script yönetici yetkileri ile çalıştırılmalıdır
    echo Sağ tıklayıp "Yönetici olarak çalıştır" seçeneğini kullanın
    pause
    exit /b 1
)

echo.
echo 📋 1. Python Kurulum Kontrolü
echo ================================

REM Python kurulumunu kontrol et
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python bulunamadı!
    echo.
    echo Python 3.8 veya üstünü yüklemeniz gerekiyor:
    echo 1. https://python.org adresine gidin
    echo 2. Python 3.8+ sürümünü indirin
    echo 3. Kurulum sırasında "Add Python to PATH" seçeneğini işaretleyin
    echo 4. Kurulum tamamlandıktan sonra bu scripti tekrar çalıştırın
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo ✅ Python bulundu: %PYTHON_VERSION%
)

echo.
echo 📋 2. Pip Güncelleme
echo ====================

echo Pip güncelleniyor...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo ⚠️  Pip güncellenirken sorun oluştu, devam ediliyor...
) else (
    echo ✅ Pip başarıyla güncellendi
)

echo.
echo 📋 3. Gerekli Paketlerin Yüklenmesi
echo ===================================

echo Python paketleri yükleniyor...
echo Bu işlem birkaç dakika sürebilir...
echo.

python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Paket yükleme sırasında hata oluştu!
    echo.
    echo Olası çözümler:
    echo 1. İnternet bağlantınızı kontrol edin
    echo 2. Antivirüs yazılımınızı geçici olarak devre dışı bırakın
    echo 3. Windows Defender'ı geçici olarak devre dışı bırakın
    echo 4. VPN kullanıyorsanız kapatmayı deneyin
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Tüm paketler başarıyla yüklendi
)

echo.
echo 📋 4. Sistem Gereksinimleri Kontrolü
echo ====================================

echo Setup checker çalıştırılıyor...
python setup_checker.py
if errorlevel 1 (
    echo ⚠️  Setup checker'da bazı sorunlar tespit edildi
    echo Lütfen yukarıdaki önerileri uygulayın
    echo.
) else (
    echo ✅ Sistem gereksinimleri kontrolü tamamlandı
)

echo.
echo 📋 5. Klasör Yapısı Oluşturma
echo =============================

REM Gerekli klasörleri oluştur
if not exist "videos" mkdir "videos"
if not exist "videos\instagramdownloaded" mkdir "videos\instagramdownloaded"
if not exist "videos\youtubedownloaded" mkdir "videos\youtubedownloaded"
if not exist "videos\twitterdownloaded" mkdir "videos\twitterdownloaded"
if not exist "videos\temp" mkdir "videos\temp"
if not exist "configuration" mkdir "configuration"
if not exist "configuration\instagram" mkdir "configuration\instagram"
if not exist "configuration\twitter" mkdir "configuration\twitter"
if not exist "live_feed_thumbnails" mkdir "live_feed_thumbnails"

echo ✅ Klasör yapısı oluşturuldu

echo.
echo 📋 6. Konfigürasyon Dosyaları
echo =============================

REM Temel konfigürasyon dosyalarını kontrol et
if not exist "configuration\instagram\default.json" (
    echo Varsayılan Instagram profili oluşturuluyor...
    echo {"username": "", "password": "", "links": [], "schedule": {}} > "configuration\instagram\default.json"
)

if not exist "configuration\twitter\default.json" (
    echo Varsayılan Twitter profili oluşturuluyor...
    echo {"username": "", "password": "", "links": [], "schedule": {}} > "configuration\twitter\default.json"
)

echo ✅ Konfigürasyon dosyaları hazırlandı

echo.
echo 📋 7. Son Kontroller
echo ====================

REM Ana dosyaların varlığını kontrol et
set MISSING_FILES=0

if not exist "main.py" (
    echo ❌ main.py eksik
    set MISSING_FILES=1
)

if not exist "ui.py" (
    echo ❌ ui.py eksik
    set MISSING_FILES=1
)

if not exist "utils.py" (
    echo ❌ utils.py eksik
    set MISSING_FILES=1
)

if %MISSING_FILES%==1 (
    echo ❌ Kritik dosyalar eksik! Programın tam sürümünü indirdiğinizden emin olun.
    pause
    exit /b 1
) else (
    echo ✅ Tüm gerekli dosyalar mevcut
)

echo.
echo ================================================================
echo 🎉 KURULUM TAMAMLANDI!
echo ================================================================
echo.
echo ✅ Python kurulumu: Tamam
echo ✅ Gerekli paketler: Yüklendi
echo ✅ Sistem gereksinimleri: Kontrol edildi
echo ✅ Klasör yapısı: Oluşturuldu
echo ✅ Konfigürasyon: Hazırlandı
echo.
echo 🚀 SorcerioModules artık kullanıma hazır!
echo.
echo Programı başlatmak için:
echo   • start_sorceriomodules.bat dosyasını çift tıklayın
echo   • Veya komut satırından: python main.py
echo.
echo 📖 Kullanım kılavuzu için README.md dosyasını okuyun
echo.
echo ⚠️  İlk çalıştırmada:
echo   • Program otomatik olarak portable Chrome indirecek
echo   • FFmpeg video işleme araçları indirilecek
echo   • Bu işlemler için internet bağlantısı gereklidir
echo.
set /p START_NOW="Programı şimdi başlatmak ister misiniz? (E/H): "
if /i "%START_NOW%"=="E" (
    echo.
    echo Program başlatılıyor...
    python main.py
) else (
    echo.
    echo Program daha sonra başlatılabilir.
    echo start_sorceriomodules.bat dosyasını kullanın.
)

echo.
pause 