@echo off
title SorcerioModules - Windows Uyumluluk Kontrolü
color 0B

echo ================================================================
echo 🔍 SorcerioModules - Windows Uyumluluk Kontrolü
echo ================================================================
echo.
echo Bu araç Windows sisteminizin SorcerioModules ile uyumluluğunu kontrol eder.
echo.

REM Windows sürüm kontrolü
echo 📋 Windows Sürüm Bilgileri
echo ================================
ver
echo.

REM Sistem mimarisi kontrolü
echo 📋 Sistem Mimarisi
echo ==================
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo ✅ 64-bit sistem tespit edildi
    set ARCH_OK=1
) else if "%PROCESSOR_ARCHITECTURE%"=="x86" (
    echo ⚠️  32-bit sistem tespit edildi
    echo    Bazı özellikler sınırlı olabilir
    set ARCH_OK=0
) else (
    echo ❌ Bilinmeyen sistem mimarisi: %PROCESSOR_ARCHITECTURE%
    set ARCH_OK=0
)
echo.

REM RAM kontrolü
echo 📋 Bellek (RAM) Kontrolü
echo ========================
for /f "tokens=2 delims==" %%i in ('wmic computersystem get TotalPhysicalMemory /value ^| find "="') do set TOTAL_RAM=%%i
set /a RAM_GB=%TOTAL_RAM:~0,-9%
if %RAM_GB% GEQ 8 (
    echo ✅ RAM: %RAM_GB% GB (Mükemmel)
    set RAM_OK=1
) else if %RAM_GB% GEQ 4 (
    echo ⚠️  RAM: %RAM_GB% GB (Minimum gereksinim karşılanıyor)
    set RAM_OK=1
) else (
    echo ❌ RAM: %RAM_GB% GB (Yetersiz - Minimum 4 GB gerekli)
    set RAM_OK=0
)
echo.

REM Disk alanı kontrolü
echo 📋 Disk Alanı Kontrolü
echo ======================
for /f "tokens=3" %%i in ('dir /-c ^| find "bytes free"') do set FREE_SPACE=%%i
set FREE_SPACE=%FREE_SPACE:,=%
set /a FREE_GB=%FREE_SPACE:~0,-9%
if %FREE_GB% GEQ 5 (
    echo ✅ Boş Disk Alanı: %FREE_GB% GB (Yeterli)
    set DISK_OK=1
) else if %FREE_GB% GEQ 2 (
    echo ⚠️  Boş Disk Alanı: %FREE_GB% GB (Minimum gereksinim)
    set DISK_OK=1
) else (
    echo ❌ Boş Disk Alanı: %FREE_GB% GB (Yetersiz - Minimum 2 GB gerekli)
    set DISK_OK=0
)
echo.

REM .NET Framework kontrolü
echo 📋 .NET Framework Kontrolü
echo ==========================
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release >nul 2>&1
if %errorlevel%==0 (
    echo ✅ .NET Framework 4.x yüklü
    set DOTNET_OK=1
) else (
    echo ⚠️  .NET Framework 4.x bulunamadı
    echo    PyQt5 için gerekli olabilir
    set DOTNET_OK=0
)
echo.

REM Visual C++ Redistributable kontrolü
echo 📋 Visual C++ Redistributable Kontrolü
echo ======================================
set VCREDIST_OK=0
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64" >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Visual C++ 2015-2019 Redistributable (x64) yüklü
    set VCREDIST_OK=1
) else (
    reg query "HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\VisualStudio\14.0\VC\Runtimes\x64" >nul 2>&1
    if %errorlevel%==0 (
        echo ✅ Visual C++ 2015-2019 Redistributable (x64) yüklü
        set VCREDIST_OK=1
    ) else (
        echo ⚠️  Visual C++ Redistributable bulunamadı
        echo    Bazı Python paketleri için gerekli olabilir
    )
)
echo.

REM Windows Defender kontrolü
echo 📋 Windows Defender Durumu
echo ==========================
sc query WinDefend | find "RUNNING" >nul
if %errorlevel%==0 (
    echo ✅ Windows Defender aktif
    echo ⚠️  Program klasörünü istisna listesine eklemeyi unutmayın
) else (
    echo ℹ️  Windows Defender durumu belirlenemedi
)
echo.

REM PowerShell sürüm kontrolü
echo 📋 PowerShell Sürüm Kontrolü
echo ============================
powershell -Command "$PSVersionTable.PSVersion.Major" >temp_ps_version.txt 2>nul
if exist temp_ps_version.txt (
    set /p PS_VERSION=<temp_ps_version.txt
    del temp_ps_version.txt
    if %PS_VERSION% GEQ 3 (
        echo ✅ PowerShell %PS_VERSION%.x yüklü
        set PS_OK=1
    ) else (
        echo ⚠️  PowerShell %PS_VERSION%.x (Eski sürüm)
        set PS_OK=0
    )
) else (
    echo ❌ PowerShell bulunamadı
    set PS_OK=0
)
echo.

REM İnternet bağlantısı kontrolü
echo 📋 İnternet Bağlantısı Kontrolü
echo ===============================
ping -n 1 google.com >nul 2>&1
if %errorlevel%==0 (
    echo ✅ İnternet bağlantısı aktif
    set INTERNET_OK=1
) else (
    echo ❌ İnternet bağlantısı yok
    echo    Kurulum için internet bağlantısı gereklidir
    set INTERNET_OK=0
)
echo.

REM Güvenlik duvarı kontrolü
echo 📋 Windows Güvenlik Duvarı
echo ==========================
netsh advfirewall show allprofiles state | find "ON" >nul
if %errorlevel%==0 (
    echo ✅ Windows Güvenlik Duvarı aktif
    echo ⚠️  Gerekirse program için istisna ekleyin
) else (
    echo ℹ️  Windows Güvenlik Duvarı kapalı veya durumu belirlenemedi
)
echo.

REM Yönetici yetkileri kontrolü
echo 📋 Yönetici Yetkileri Kontrolü
echo ==============================
net session >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Yönetici yetkileri mevcut
    set ADMIN_OK=1
) else (
    echo ⚠️  Yönetici yetkileri yok
    echo    Kurulum için yönetici yetkileri önerilir
    set ADMIN_OK=0
)
echo.

REM Genel uyumluluk değerlendirmesi
echo ================================================================
echo 📊 GENEL UYUMLULUK DEĞERLENDİRMESİ
echo ================================================================
echo.

set TOTAL_SCORE=0
set MAX_SCORE=8

if %ARCH_OK%==1 set /a TOTAL_SCORE+=1
if %RAM_OK%==1 set /a TOTAL_SCORE+=1
if %DISK_OK%==1 set /a TOTAL_SCORE+=1
if %DOTNET_OK%==1 set /a TOTAL_SCORE+=1
if %VCREDIST_OK%==1 set /a TOTAL_SCORE+=1
if %PS_OK%==1 set /a TOTAL_SCORE+=1
if %INTERNET_OK%==1 set /a TOTAL_SCORE+=1
if %ADMIN_OK%==1 set /a TOTAL_SCORE+=1

echo Uyumluluk Skoru: %TOTAL_SCORE%/%MAX_SCORE%
echo.

if %TOTAL_SCORE% GEQ 7 (
    echo 🎉 MÜKEMMEL UYUMLULUK!
    echo ✅ Sisteminiz SorcerioModules için tamamen uyumlu
    echo ✅ Kurulum sorunsuz gerçekleşecektir
    echo.
    echo 🚀 Kuruluma başlamak için auto_setup.bat dosyasını çalıştırın
) else if %TOTAL_SCORE% GEQ 5 (
    echo ⚠️  İYİ UYUMLULUK
    echo ✅ Sisteminiz SorcerioModules ile uyumlu
    echo ⚠️  Bazı küçük sorunlar yaşayabilirsiniz
    echo.
    echo 🔧 Öneriler:
    if %DOTNET_OK%==0 echo    • .NET Framework 4.8 yükleyin
    if %VCREDIST_OK%==0 echo    • Visual C++ Redistributable yükleyin
    if %ADMIN_OK%==0 echo    • Kurulumu yönetici olarak çalıştırın
    if %INTERNET_OK%==0 echo    • İnternet bağlantınızı kontrol edin
) else (
    echo ❌ UYUMLULUK SORUNLARI
    echo ⚠️  Sisteminizde önemli uyumluluk sorunları var
    echo 🔧 Aşağıdaki sorunları çözün:
    echo.
    if %ARCH_OK%==0 echo    • 64-bit Windows kullanmanız önerilir
    if %RAM_OK%==0 echo    • En az 4 GB RAM gereklidir
    if %DISK_OK%==0 echo    • En az 2 GB boş disk alanı gereklidir
    if %INTERNET_OK%==0 echo    • İnternet bağlantısı gereklidir
    echo.
    echo Bu sorunları çözdükten sonra kurulumu deneyin.
)

echo.
echo ================================================================
echo 📋 KURULUM ÖNERİLERİ
echo ================================================================
echo.
echo 1. 🔧 Sistem Hazırlığı:
echo    • Windows güncellemelerini yükleyin
echo    • Antivirüs yazılımınızı geçici olarak kapatın
echo    • Program klasörünü antivirüs istisnalarına ekleyin
echo.
echo 2. 🐍 Python Kurulumu:
echo    • Python 3.9 veya 3.10 önerilir
echo    • Kurulum sırasında "Add Python to PATH" seçeneğini işaretleyin
echo    • Kurulum sonrası bilgisayarı yeniden başlatın
echo.
echo 3. 🚀 SorcerioModules Kurulumu:
echo    • auto_setup.bat dosyasını yönetici olarak çalıştırın
echo    • Kurulum sırasında internet bağlantısını açık tutun
echo    • İlk çalıştırmada portable Chrome indirilecektir
echo.
echo 4. 🔒 Güvenlik Ayarları:
echo    • Windows Defender'da program klasörünü istisna ekleyin
echo    • Güvenlik duvarında gerekirse istisna ekleyin
echo    • Antivirüs yazılımında istisna ekleyin
echo.

echo ================================================================
pause 