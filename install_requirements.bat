@echo off
echo ================================================
echo SorcerioModules Dependency Installer
echo ================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo Python found. Installing dependencies...
echo.

REM Upgrade pip first
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install requirements
echo.
echo Installing SorcerioModules dependencies...
python -m pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ERROR: Failed to install some dependencies
    echo Please check the error messages above
    pause
    exit /b 1
)

echo.
echo ================================================
echo Dependencies installed successfully!
echo ================================================
echo.
echo You can now run the setup checker:
echo python setup_checker.py
echo.
echo Or start the application directly:
echo python main.py
echo.
pause
